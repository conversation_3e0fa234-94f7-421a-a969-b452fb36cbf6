import numpy as np
import torch
from torch_geometric.data import (InMemoryDataset, Data)
from .lmdb_dataset import atomic_number_reverse
from torch.utils.data import Subset
from functools import lru_cache
from unicore.data import BaseWrapperDataset
from torch_geometric.data import Data

class DAVISDataset(InMemoryDataset):
    def __init__(self, data_path, split,  transform_noise=None, lp_sep=False):

        self.transform_noise = transform_noise
        npyfile = data_path + f"_{split}.npy"
        self.data_dict = np.load(npyfile, allow_pickle=True).item() # dict
        self.length = len(self.data_dict['index'])
        self.lp_sep = lp_sep
        self.pocket_atom_offset = 120

    def __len__(self) -> int:
        return self.length

    def __getitem__(self, idx):

        data = Data()
        num_atoms = self.data_dict['num_atoms'][idx]
        pocket_atomsnum = self.data_dict['pocket_atoms'][idx]
        ligand_atomsnum = self.data_dict['ligand_atoms'][idx]
        assert (pocket_atomsnum + ligand_atomsnum) == num_atoms

        data.z = torch.tensor(self.data_dict['charges'][idx][:num_atoms], dtype=torch.long)
        data.pos = torch.tensor(self.data_dict['positions'][idx][:num_atoms], dtype=torch.float32)
        if self.transform_noise is not None:
            data = self.transform_noise(data) # noisy node

        data.affinity =  torch.tensor(self.data_dict['neglog_aff'][idx], dtype=torch.float32)

        data.org_pos = torch.tensor(self.data_dict['positions'][idx][:num_atoms], dtype=torch.float32)

        data.pocket_atomsnum = pocket_atomsnum

        # type mask
        poc_lig_id = np.zeros(num_atoms)
        poc_lig_id[pocket_atomsnum: ] = 1 # lig 1
        data.type_mask = torch.tensor(poc_lig_id, dtype=torch.long)

        return data


def make_DAVIS_dataset(data, idx_path, max_num, split, split_ratio=0.95):
    raw_dataset = DAVISDataset(data_path=data, split=split)
    org_data_len = len(raw_dataset)
    org_idx = np.array([idx for idx in range(org_data_len)])
    filter_idx = org_idx
    data_idx = filter_idx

    return Subset(raw_dataset, data_idx)  # 根据data_idx做slice



class ExtractDAVISDataset(BaseWrapperDataset):
    '''Return dataset to input in the model and target for training'''
    def __init__(self, dataset, smi, atoms, coordinates, task='complex_pretrain'):
        self.dataset = dataset
        self.smi = smi
        self.atoms = atoms
        self.coordinates = coordinates
        self.task = task
        self.set_epoch(None)

    def set_epoch(self, epoch, **unused):
        super().set_epoch(epoch)
        self.epoch = epoch

    @lru_cache(maxsize=16)
    def __cached_item__(self, index: int, epoch: int):
        data = self.dataset[index]
        all_atoms_z = data.z
        all_atoms_pos = data.pos
        type_mask = data.type_mask.to(torch.bool)  # type_mask: ligand 1, protein 0

        # Protein Part
        protein_atoms_z = all_atoms_z[~type_mask].numpy()
        protein_atoms_pos = all_atoms_pos[~type_mask].numpy()
        protein_num = (~type_mask).sum().item()

        # Ligand Part
        ligand_atoms_z = all_atoms_z[type_mask].numpy()
        ligand_atom_pos = all_atoms_pos[type_mask].numpy()
        ligand_num = type_mask.sum().item()

        # Get Atom from Atom_z index
        protein_atoms_sym = np.array([atomic_number_reverse[ele] for ele in protein_atoms_z])
        ligand_atoms_sym = np.array([atomic_number_reverse[ele] for ele in ligand_atoms_z])

        # Get affinity
        affinity = data.affinity

        return {"smi": "", "atoms": protein_atoms_sym, "protein_atoms_pos": protein_atoms_pos, 'atoms_lig': ligand_atoms_sym, "ligand_atom_pos":ligand_atom_pos,
                "all_coordinate": all_atoms_pos, "prot_num": protein_num, "lig_num": ligand_num, 'affinity': affinity, "lig_atoms_z": ligand_atoms_z}


    def __getitem__(self, index: int):
        return self.__cached_item__(index, self.epoch)
