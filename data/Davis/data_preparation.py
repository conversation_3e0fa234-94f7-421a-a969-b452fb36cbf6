#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Data preparation script for Davis dataset
Converts full.csv and PDB files to train.npy, test.npy, valid.npy format
"""

import os
import numpy as np
import pandas as pd
from tqdm import tqdm
from multiprocessing import Pool, cpu_count
import argparse
from functools import partial
import warnings
warnings.filterwarnings('ignore')

# PDBProtein class (copied from crossdocked/data_preparation.py to avoid import issues)
class PDBProtein(object):
    AA_NAME_SYM = {'ALA': 'A', 'ARG': 'R', 'ASN': 'N', 'ASP': 'D', 'CYS': 'C', 'GLN': 'Q', 'GLU': 'E', 'GLY': 'G',
                   'HIS': 'H', 'ILE': 'I', 'LEU': 'L', 'LYS': 'K', 'MET': 'M', 'PHE': 'F', 'PRO': 'P', 'SER': 'S',
                   'THR': 'T', 'TRP': 'W', 'TYR': 'Y', 'VAL': 'V'}

    AA_NAME_NUMBER = {
        k: i + 1 for i, (k, _) in enumerate(AA_NAME_SYM.items())
    }

    BACKBONE_NAMES = ["CA", "C", "N", "O"]

    def __init__(self, data, mode='auto'):
        super().__init__()
        if (data[-4:].lower() == '.pdb' and mode == 'auto') or mode == 'path':
            with open(data, 'r') as f:
                self.block = f.read()
        else:
            self.block = data

        self.ptable = Chem.GetPeriodicTable()

        # Molecule properties
        self.title = None
        # Atom properties
        self.atoms = []
        self.element = []
        self.atomic_weight = []
        self.pos = []
        self.atom_name = []
        self.is_backbone = []
        self.atom_to_aa_type = []
        self.atom2residue = []
        # Residue properties
        self.residues = []
        self.amino_acid = []
        self.amino_idx = []
        self.center_of_mass = []
        self.pos_CA = []
        self.pos_C = []
        self.pos_N = []
        self.pos_O = []
        self.residue_natoms = []
        self.seq = []

        self._parse()

    def _enum_formatted_atom_lines(self):
        for line in self.block.splitlines():
            if line.startswith("HEADER"):
                yield {
                    "type": "HEADER",
                    "value": line[10:].strip()
                }
                continue
            if not line.startswith("ATOM"):
                yield {
                    "type": "others",
                    "value": line
                }
                continue
            yield {
                "type": "ATOM",
                "atom_id": int(line[6:11]),
                "atom_name": line[12:16].strip(),
                "res_name": line[17:20].strip(),
                "chain": line[21:22].strip(),
                "res_id": int(line[22:26]),
                "res_insert_id": line[26:27].strip(),
                "x": float(line[30:38]),
                "y": float(line[38:46]),
                "z": float(line[46:54]),
                "occupancy": float(line[54:60]) if line[54:60].strip() else 1.0,
                "temp_factor": float(line[60:66]) if line[60:66].strip() else 0.0,
                "element_symb": line[76:78].strip(),
                "line": line.strip()
            }

    def _parse(self):
        # Process atoms
        residues_tmp = {}
        num_residue = -1
        for atom in self._enum_formatted_atom_lines():
            if atom['type'] == 'HEADER':
                self.title = atom['value'].lower()
                continue
            if atom['type'] == 'others':
                continue
            if atom['atom_name'][0] == 'H' or atom['atom_name'] == 'OXT':
                continue
            self.atoms.append(atom)
            atomic_number = self.ptable.GetAtomicNumber(atom['element_symb'])
            next_ptr = len(self.element)
            self.element.append(atomic_number)
            self.atomic_weight.append(self.ptable.GetAtomicWeight(atomic_number))
            self.pos.append(np.array([atom['x'], atom['y'], atom['z']], dtype=np.float32))
            self.atom_name.append(atom['atom_name'])
            self.is_backbone.append(atom['atom_name'] in self.BACKBONE_NAMES)

            # Residue info
            res_key = (atom['chain'], atom['res_id'], atom['res_insert_id'])
            if res_key not in residues_tmp:
                num_residue += 1
                residues_tmp[res_key] = {
                    'name': atom['res_name'], 'atoms': [], 'chain': atom['chain'],
                    'id': atom['res_id'], 'insert_id': atom['res_insert_id']
                }
            residues_tmp[res_key]['atoms'].append(next_ptr)
            self.atom2residue.append(num_residue)

        self.residues = list(residues_tmp.values())

        # Process residues
        for residue in self.residues:
            sum_pos = np.array([0.0, 0.0, 0.0])
            sum_mass = 0.0
            res_atoms = residue['atoms']
            residue['n_atoms'] = len(res_atoms)
            for atom_idx in res_atoms:
                atomic_weight = self.atomic_weight[atom_idx]
                sum_pos += self.pos[atom_idx] * atomic_weight
                sum_mass += atomic_weight

            residue['center_of_mass'] = sum_pos / sum_mass

            # Find backbone atoms
            for atom_idx in res_atoms:
                atom_name = self.atom_name[atom_idx]
                if atom_name in self.BACKBONE_NAMES:
                    residue[f'pos_{atom_name}'] = self.pos[atom_idx]

    def query_residues_ligand(self, ligand, radius, selected_residue=None, return_mask=False):
        if selected_residue is None:
            selected_residue = self.residues

        # Get ligand center
        if isinstance(ligand['pos'][0], (list, np.ndarray)):
            ligand_center = np.mean(ligand['pos'], axis=0)
        else:
            ligand_center = ligand['pos'][0]

        selected_residue_idx = []
        selected_residues = []

        for i, residue in enumerate(selected_residue):
            distance = np.linalg.norm(residue['center_of_mass'] - ligand_center)
            if distance <= radius:
                selected_residue_idx.append(i)
                selected_residues.append(residue)

        if return_mask:
            mask = np.zeros(len(selected_residue), dtype=bool)
            mask[selected_residue_idx] = True
            return mask, selected_residues
        else:
            return selected_residue_idx, selected_residues

# RDKit imports
from rdkit import Chem
from rdkit.Chem import AllChem
from rdkit import RDLogger
RDLogger.DisableLog('rdApp.*')


def smiles_to_3d_coords(smiles, max_attempts=1000):
    """
    Convert SMILES to 3D coordinates using RDKit
    """
    try:
        mol = Chem.MolFromSmiles(smiles)
        if mol is None:
            return None, None

        # Add hydrogens and generate 3D coordinates
        mol = Chem.AddHs(mol)

        # Generate 3D coordinates
        result = AllChem.EmbedMolecule(mol, maxAttempts=max_attempts, randomSeed=42)
        if result != 0:
            return None, None

        # Optimize geometry
        AllChem.MMFFOptimizeMolecule(mol)

        # Remove hydrogens to match expected format
        mol = Chem.RemoveHs(mol)

        # Extract atomic numbers and coordinates
        atomic_numbers = []
        coordinates = []

        for atom in mol.GetAtoms():
            atomic_numbers.append(atom.GetAtomicNum())

        conf = mol.GetConformer()
        for i in range(mol.GetNumAtoms()):
            pos = conf.GetAtomPosition(i)
            coordinates.append([pos.x, pos.y, pos.z])

        return np.array(atomic_numbers), np.array(coordinates, dtype=np.float32)

    except Exception as e:
        print(f"Error processing SMILES {smiles}: {e}")
        return None, None


def process_protein_pdb(pdb_path, ligand_coords, pocket_radius=15.0):
    """
    Process protein PDB file and extract pocket atoms around ligand
    """
    try:
        protein = PDBProtein(pdb_path, mode='path')

        # Create a fake ligand object for pocket extraction
        ligand_center = np.mean(ligand_coords, axis=0)
        fake_ligand = {
            'pos': [ligand_center],  # Use center of ligand
            'element': [6]  # Carbon
        }

        # Get pocket residues within radius
        _, pocket_residues = protein.query_residues_ligand(
            fake_ligand, pocket_radius, selected_residue=None, return_mask=False
        )

        # If no residues found with current radius, try a larger radius
        if len(pocket_residues) == 0:
            _, pocket_residues = protein.query_residues_ligand(
                fake_ligand, 100.0, selected_residue=None, return_mask=False
            )

        # Extract pocket atoms and coordinates
        pocket_atomic_numbers = []
        pocket_coordinates = []

        for residue in pocket_residues:
            for atom_idx in residue['atoms']:
                pocket_atomic_numbers.append(protein.element[atom_idx])
                pocket_coordinates.append(protein.pos[atom_idx])

        if len(pocket_atomic_numbers) == 0:
            print(f"Warning: No pocket atoms found even with larger radius for {pdb_path}")
            print(f"Ligand center: {ligand_center}")
            print(f"Total protein residues: {len(protein.residues)}")
            if len(protein.residues) > 0:
                print(f"First residue center: {protein.residues[0]['center_of_mass']}")

        return np.array(pocket_atomic_numbers), np.array(pocket_coordinates, dtype=np.float32)

    except Exception as e:
        print(f"Error processing PDB {pdb_path}: {e}")
        return None, None


def process_single_entry(entry_data, pdb_dir="./prot_3d_for_Davis", pocket_radius=15.0):
    """
    Process a single entry from the dataset
    """
    idx, smiles, protein_name, affinity = entry_data

    # Convert SMILES to 3D coordinates
    ligand_atomic_numbers, ligand_coords = smiles_to_3d_coords(smiles)
    if ligand_atomic_numbers is None:
        return None

    # Process protein PDB
    pdb_path = os.path.join(pdb_dir, f"{protein_name}.pdb")
    if not os.path.exists(pdb_path):
        print(f"PDB file not found: {pdb_path}")
        return None

    pocket_atomic_numbers, pocket_coords = process_protein_pdb(pdb_path, ligand_coords, pocket_radius)
    if pocket_atomic_numbers is None:
        return None

    # Handle empty pocket case
    if len(pocket_atomic_numbers) == 0:
        print(f"Warning: No pocket atoms found for protein {protein_name}, skipping...")
        return None

    # Combine pocket and ligand atoms
    all_atomic_numbers = np.concatenate([pocket_atomic_numbers, ligand_atomic_numbers])

    # Ensure both coordinate arrays are 2D: [n_atoms, 3]
    if pocket_coords.ndim == 1:
        if len(pocket_coords) == 0:
            # Empty array, reshape to (0, 3)
            pocket_coords = pocket_coords.reshape(0, 3)
        else:
            # Single atom, reshape to (1, 3)
            pocket_coords = pocket_coords.reshape(1, -1)

    if ligand_coords.ndim == 1:
        if len(ligand_coords) == 0:
            # Empty array, reshape to (0, 3)
            ligand_coords = ligand_coords.reshape(0, 3)
        else:
            # Single atom, reshape to (1, 3)
            ligand_coords = ligand_coords.reshape(1, -1)

    all_coordinates = np.concatenate([pocket_coords, ligand_coords], axis=0)

    # Create data entry
    data_entry = {
        'index': idx,
        'num_atoms': len(all_atomic_numbers),
        'pocket_atoms': len(pocket_atomic_numbers),
        'ligand_atoms': len(ligand_atomic_numbers),
        'charges': all_atomic_numbers,
        'positions': all_coordinates,
        'neglog_aff': float(affinity),  # Already in negative log format
        'smiles': smiles,
        'protein_name': protein_name
    }

    return data_entry


def create_data_dict(processed_entries):
    """
    Create the data dictionary in the format expected by DAVISDataset
    """
    # Filter out None entries
    valid_entries = [entry for entry in processed_entries if entry is not None]

    if not valid_entries:
        raise ValueError("No valid entries found")

    # Initialize data dictionary
    data_dict = {
        'index': [],
        'num_atoms': [],
        'pocket_atoms': [],
        'ligand_atoms': [],
        'charges': [],
        'positions': [],
        'neglog_aff': [],
        'smiles': [],
        'protein_names': []
    }

    # Find maximum number of atoms for padding
    max_atoms = max(entry['num_atoms'] for entry in valid_entries)

    for entry in valid_entries:
        data_dict['index'].append(entry['index'])
        data_dict['num_atoms'].append(entry['num_atoms'])
        data_dict['pocket_atoms'].append(entry['pocket_atoms'])
        data_dict['ligand_atoms'].append(entry['ligand_atoms'])
        data_dict['neglog_aff'].append(entry['neglog_aff'])
        data_dict['smiles'].append(entry['smiles'])
        data_dict['protein_names'].append(entry['protein_name'])

        # Pad charges and positions to max_atoms
        charges = np.zeros(max_atoms, dtype=np.int32)
        positions = np.zeros((max_atoms, 3), dtype=np.float32)

        n_atoms = entry['num_atoms']
        charges[:n_atoms] = entry['charges']
        positions[:n_atoms] = entry['positions']

        data_dict['charges'].append(charges)
        data_dict['positions'].append(positions)

    # Convert lists to numpy arrays
    for key in ['index', 'num_atoms', 'pocket_atoms', 'ligand_atoms', 'neglog_aff']:
        data_dict[key] = np.array(data_dict[key])

    data_dict['charges'] = np.array(data_dict['charges'])
    data_dict['positions'] = np.array(data_dict['positions'])

    return data_dict


def split_dataset(data_dict, train_ratio=0.8, valid_ratio=0.1, random_seed=42):
    """
    Split dataset into train/valid/test sets
    """
    np.random.seed(random_seed)

    n_samples = len(data_dict['index'])
    indices = np.arange(n_samples)
    np.random.shuffle(indices)

    # Calculate split points
    train_end = int(n_samples * train_ratio)
    valid_end = train_end + int(n_samples * valid_ratio)

    train_indices = indices[:train_end]
    valid_indices = indices[train_end:valid_end]
    test_indices = indices[valid_end:]

    def create_split_dict(split_indices):
        split_dict = {}
        for key, values in data_dict.items():
            if key in ['smiles', 'protein_names']:
                split_dict[key] = [values[i] for i in split_indices]
            else:
                split_dict[key] = values[split_indices]
        return split_dict

    train_dict = create_split_dict(train_indices)
    valid_dict = create_split_dict(valid_indices)
    test_dict = create_split_dict(test_indices)

    return train_dict, valid_dict, test_dict


def main():
    parser = argparse.ArgumentParser(description='Prepare Davis dataset')
    parser.add_argument('--csv_path', type=str, default='./full.csv',
                        help='Path to full.csv file')
    parser.add_argument('--pdb_dir', type=str, default='./prot_3d_for_Davis',
                        help='Directory containing PDB files')
    parser.add_argument('--output_dir', type=str, default='./data',
                        help='Output directory for .npy files')
    parser.add_argument('--pocket_radius', type=float, default=15.0,
                        help='Radius for pocket extraction (Angstroms)')
    parser.add_argument('--num_workers', type=int, default=min(cpu_count(), 8),
                        help='Number of parallel workers')
    parser.add_argument('--train_ratio', type=float, default=0.8,
                        help='Training set ratio')
    parser.add_argument('--valid_ratio', type=float, default=0.1,
                        help='Validation set ratio')
    parser.add_argument('--test_ratio', type=float, default=0.1,
                        help='Test set ratio')
    parser.add_argument('--max_samples', type=int, default=None,
                        help='Maximum number of samples to process (for testing)')

    args = parser.parse_args()

    # Validate ratios
    if abs(args.train_ratio + args.valid_ratio + args.test_ratio - 1.0) > 1e-6:
        raise ValueError("Train, valid, and test ratios must sum to 1.0")

    print("Loading CSV data...")
    df = pd.read_csv(args.csv_path)
    print(f"Loaded {len(df)} entries from CSV")

    # Limit samples if specified
    if args.max_samples:
        df = df.head(args.max_samples)
        print(f"Limited to {len(df)} samples for testing")

    # Prepare data for processing
    entries_data = []
    for idx, row in df.iterrows():
        entries_data.append((idx, row['ligand'], row['protein'], row['label']))

    print(f"Processing {len(entries_data)} entries...")

    # Process entries
    if args.num_workers > 1:
        print(f"Using {args.num_workers} parallel workers")
        process_func = partial(process_single_entry, pdb_dir=args.pdb_dir, pocket_radius=args.pocket_radius)
        with Pool(args.num_workers) as pool:
            processed_entries = list(tqdm(
                pool.imap(process_func, entries_data),
                total=len(entries_data),
                desc="Processing entries"
            ))
    else:
        print("Using single worker")
        processed_entries = []
        for entry_data in tqdm(entries_data, desc="Processing entries"):
            processed_entries.append(process_single_entry(entry_data, args.pdb_dir, args.pocket_radius))

    print("Creating data dictionary...")
    data_dict = create_data_dict(processed_entries)

    valid_count = len(data_dict['index'])
    print(f"Successfully processed {valid_count} out of {len(entries_data)} entries")

    if valid_count == 0:
        raise ValueError("No valid entries found. Check your data and processing pipeline.")

    print("Splitting dataset...")
    train_dict, valid_dict, test_dict = split_dataset(
        data_dict, args.train_ratio, args.valid_ratio
    )

    print(f"Train set: {len(train_dict['index'])} samples")
    print(f"Valid set: {len(valid_dict['index'])} samples")
    print(f"Test set: {len(test_dict['index'])} samples")

    # Save datasets
    os.makedirs(args.output_dir, exist_ok=True)

    train_path = os.path.join(args.output_dir, 'data_train.npy')
    valid_path = os.path.join(args.output_dir, 'data_valid.npy')
    test_path = os.path.join(args.output_dir, 'data_test.npy')

    print("Saving datasets...")
    np.save(train_path, train_dict, allow_pickle=True)
    np.save(valid_path, valid_dict, allow_pickle=True)
    np.save(test_path, test_dict, allow_pickle=True)

    print(f"Saved train set to: {train_path}")
    print(f"Saved valid set to: {valid_path}")
    print(f"Saved test set to: {test_path}")

    print("Data preparation completed successfully!")


if __name__ == "__main__":
    main()